# LLM Training Project - Design Document

## Overview

本项目实现一个轻量级的大语言模型训练系统，使用PyTorch框架构建。系统包含四个核心模块：模型架构、分词器、训练引擎和推理引擎。整体设计遵循模块化原则，确保各组件可独立测试和复用。

项目采用Transformer架构的简化版本，参数量控制在10万左右，适合在普通硬件上训练。训练数据使用简单的文本语料，推理阶段支持基于命令行的交互式对话。

## Architecture

### System Architecture

```mermaid
graph TB
    A[Text Data] --> B[Tokenizer]
    B --> C[Data Loader]
    C --> D[Training Engine]
    D --> E[Model Architecture]
    E --> F[Checkpoint]
    F --> G[Inference Engine]
    G --> H[Generated Text]
    B --> G
    
    style E fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style G fill:#bfb,stroke:#333,stroke-width:2px
```

### Component Interaction Flow

1. **训练阶段**：Text Data → Tokenizer → Data Loader → Training Engine → Model → Checkpoint
2. **推理阶段**：User Input → Tokenizer → Inference Engine → Model → Generated Tokens → Tokenizer → Text Output

## Components and Interfaces

### 1. Model Architecture (`model.py`)

**核心类：`SimpleLLM`**

```python
class SimpleLLM(nn.Module):
    """
    简化的Transformer语言模型
    
    参数配置（目标：~100k参数）：
    - vocab_size: 2000 (词汇表大小)
    - embed_dim: 128 (嵌入维度)
    - num_heads: 4 (注意力头数)
    - num_layers: 2 (Transformer层数)
    - ff_dim: 256 (前馈网络维度)
    - max_seq_len: 64 (最大序列长度)
    
    参数量计算：
    - Token Embedding: 2000 * 128 = 256,000
    - Position Embedding: 64 * 128 = 8,192
    - 每个Transformer层: ~25,000
    - 总计: 约100k (通过调整embed_dim到64可降至目标范围)
    """
    
    def __init__(self, config):
        # 嵌入层
        self.token_embedding
        self.position_embedding
        
        # Transformer层
        self.transformer_blocks
        
        # 输出层
        self.ln_f  # Layer Normalization
        self.lm_head  # 语言模型头
    
    def forward(self, input_ids, attention_mask=None):
        """前向传播，返回logits"""
        pass
```

**关键设计决策**：
- 使用较小的嵌入维度（64-128）来控制参数量
- 采用2层Transformer以平衡性能和复杂度
- 使用PyTorch的`nn.TransformerEncoderLayer`简化实现
- 共享token embedding和输出层权重以减少参数

### 2. Tokenizer (`tokenizer.py`)

**核心类：`SimpleTokenizer`**

```python
class SimpleTokenizer:
    """
    基于字符或简单词级别的分词器
    
    特殊Token：
    - <PAD>: 0 (填充)
    - <UNK>: 1 (未知词)
    - <BOS>: 2 (序列开始)
    - <EOS>: 3 (序列结束)
    """
    
    def __init__(self, vocab_size=2000):
        self.vocab_size = vocab_size
        self.token_to_id = {}
        self.id_to_token = {}
        self.special_tokens = ['<PAD>', '<UNK>', '<BOS>', '<EOS>']
    
    def build_vocab(self, texts):
        """从文本构建词汇表"""
        pass
    
    def encode(self, text):
        """文本 -> token IDs"""
        pass
    
    def decode(self, token_ids):
        """token IDs -> 文本"""
        pass
    
    def save(self, path):
        """保存词汇表"""
        pass
    
    def load(self, path):
        """加载词汇表"""
        pass
```

**实现策略**：
- 初期使用简单的空格分词 + 词频统计
- 保留最常见的N个词（N=vocab_size-4，减去特殊token）
- 支持序列化为JSON格式

### 3. Data Processing (`data.py`)

**核心类：`TextDataset` 和 `DataProcessor`**

```python
class TextDataset(Dataset):
    """PyTorch Dataset for text data"""
    
    def __init__(self, texts, tokenizer, max_length=64):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.examples = self._prepare_examples(texts)
    
    def _prepare_examples(self, texts):
        """将文本转换为训练样本（input, target）"""
        pass
    
    def __getitem__(self, idx):
        return self.examples[idx]
    
    def __len__(self):
        return len(self.examples)

class DataProcessor:
    """数据加载和预处理"""
    
    @staticmethod
    def load_text_file(file_path):
        """从文件加载文本"""
        pass
    
    @staticmethod
    def create_dataloaders(dataset, batch_size=16, train_split=0.9):
        """创建训练和验证DataLoader"""
        pass
```

**数据格式**：
- 输入：连续文本，按句子或段落分割
- 训练样本：(input_sequence, target_sequence)，其中target是input右移一位
- 批处理：自动padding到batch内最大长度

### 4. Training Engine (`train.py`)

**核心类：`Trainer`**

```python
class Trainer:
    """模型训练器"""
    
    def __init__(self, model, train_loader, val_loader, config):
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.config = config
        
        # 优化器和损失函数
        self.optimizer = torch.optim.Adam(model.parameters(), lr=config.learning_rate)
        self.criterion = nn.CrossEntropyLoss(ignore_index=0)  # 忽略padding
        
        # 训练状态
        self.current_epoch = 0
        self.global_step = 0
        self.best_val_loss = float('inf')
    
    def train_epoch(self):
        """训练一个epoch"""
        pass
    
    def validate(self):
        """在验证集上评估"""
        pass
    
    def train(self, num_epochs):
        """完整训练流程"""
        pass
    
    def save_checkpoint(self, path):
        """保存检查点"""
        pass
    
    def load_checkpoint(self, path):
        """加载检查点"""
        pass
```

**训练配置**：
```python
TrainingConfig:
    - learning_rate: 0.001
    - batch_size: 16
    - num_epochs: 20
    - max_seq_length: 64
    - gradient_clip: 1.0
    - checkpoint_interval: 5 (每5个epoch保存一次)
    - log_interval: 10 (每10个batch打印一次)
```

**训练流程**：
1. 遍历训练数据批次
2. 前向传播计算loss
3. 反向传播计算梯度
4. 梯度裁剪（防止梯度爆炸）
5. 优化器更新参数
6. 定期验证和保存检查点

### 5. Inference Engine (`inference.py`)

**核心类：`TextGenerator`**

```python
class TextGenerator:
    """文本生成器"""
    
    def __init__(self, model, tokenizer, device='cpu'):
        self.model = model
        self.tokenizer = tokenizer
        self.device = device
        self.model.eval()
    
    def generate(self, prompt, max_length=50, temperature=1.0, top_k=50):
        """
        生成文本
        
        参数：
        - prompt: 输入提示文本
        - max_length: 最大生成长度
        - temperature: 采样温度（控制随机性）
        - top_k: Top-K采样
        """
        pass
    
    def chat(self):
        """交互式对话循环"""
        pass
```

**生成策略**：
- **Greedy Decoding**：选择概率最高的token（temperature=0）
- **Sampling**：根据概率分布采样（temperature>0）
- **Top-K Sampling**：只从概率最高的K个token中采样

**对话接口**：
```
> 你好
模型: [生成的回复]
> 今天天气怎么样
模型: [生成的回复]
> exit
```

### 6. Main Entry Point (`main.py`)

```python
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--mode', choices=['train', 'chat'], required=True)
    parser.add_argument('--data', type=str, help='训练数据路径')
    parser.add_argument('--checkpoint', type=str, help='模型检查点路径')
    # ... 其他参数
    
    args = parser.parse_args()
    
    if args.mode == 'train':
        # 训练模式
        train_model(args)
    elif args.mode == 'chat':
        # 对话模式
        chat_with_model(args)
```

## Data Models

### Model Configuration

```python
@dataclass
class ModelConfig:
    vocab_size: int = 2000
    embed_dim: int = 64
    num_heads: int = 4
    num_layers: int = 2
    ff_dim: int = 128
    max_seq_len: int = 64
    dropout: float = 0.1
```

### Training State

```python
@dataclass
class TrainingState:
    epoch: int
    global_step: int
    best_val_loss: float
    train_losses: List[float]
    val_losses: List[float]
```

### Checkpoint Format

```python
checkpoint = {
    'model_state_dict': model.state_dict(),
    'optimizer_state_dict': optimizer.state_dict(),
    'config': model_config,
    'training_state': training_state,
    'tokenizer_vocab': tokenizer.get_vocab()
}
```

## Error Handling

### 训练阶段错误处理

1. **数据加载错误**
   - 文件不存在：提示用户检查路径
   - 数据格式错误：跳过无效样本，记录警告
   - 内存不足：建议减小batch_size

2. **训练过程错误**
   - NaN loss：降低学习率，检查数据质量
   - CUDA OOM：自动回退到CPU或减小batch_size
   - 梯度爆炸：应用梯度裁剪

3. **检查点错误**
   - 保存失败：确保目录存在且有写权限
   - 加载失败：验证文件完整性，提供详细错误信息

### 推理阶段错误处理

1. **模型加载错误**
   - 检查点不存在：提示用户先训练模型
   - 配置不匹配：拒绝加载并说明原因

2. **生成错误**
   - 空输入：提示用户输入有效文本
   - 生成超时：设置最大生成步数限制
   - 无效token：使用<UNK>替代

## Testing Strategy

### Unit Tests

1. **Tokenizer Tests**
   - 测试encode/decode的可逆性
   - 测试特殊token处理
   - 测试词汇表保存/加载

2. **Model Tests**
   - 测试模型初始化
   - 测试前向传播输出形状
   - 测试参数量是否符合预期

3. **Data Processing Tests**
   - 测试数据集创建
   - 测试批处理和padding
   - 测试训练/验证集分割

### Integration Tests

1. **训练流程测试**
   - 使用小数据集进行快速训练
   - 验证loss是否下降
   - 验证检查点保存/加载

2. **推理流程测试**
   - 测试文本生成功能
   - 验证生成文本的合理性
   - 测试不同生成参数的效果

### Manual Testing

1. **训练监控**
   - 观察训练loss曲线
   - 检查验证集perplexity
   - 监控训练时间和资源使用

2. **对话质量**
   - 测试多轮对话连贯性
   - 评估回复的相关性
   - 检查是否有重复生成问题

## Performance Considerations

### 训练优化

1. **数据加载**
   - 使用DataLoader的num_workers进行多进程加载
   - 预先tokenize数据并缓存

2. **模型优化**
   - 使用混合精度训练（如果GPU支持）
   - 梯度累积以模拟更大的batch size

3. **内存管理**
   - 及时清理不需要的中间变量
   - 使用gradient checkpointing（如果需要）

### 推理优化

1. **生成速度**
   - 使用torch.no_grad()禁用梯度计算
   - 批量生成多个样本
   - 缓存position embeddings

2. **模型压缩**
   - 量化模型权重（可选）
   - 剪枝不重要的连接（可选）

## Deployment Considerations

### 环境要求

```
Python >= 3.8
PyTorch >= 1.10
numpy
tqdm (进度条)
```

### 文件结构

```
llm-training-project/
├── src/
│   ├── model.py          # 模型架构
│   ├── tokenizer.py      # 分词器
│   ├── data.py           # 数据处理
│   ├── train.py          # 训练引擎
│   ├── inference.py      # 推理引擎
│   └── config.py         # 配置定义
├── data/
│   └── train.txt         # 训练数据
├── checkpoints/          # 模型检查点
├── logs/                 # 训练日志
├── main.py               # 主入口
├── requirements.txt      # 依赖
└── README.md            # 使用说明
```

### 使用流程

1. **准备数据**：将训练文本放入`data/train.txt`
2. **训练模型**：`python main.py --mode train --data data/train.txt`
3. **对话测试**：`python main.py --mode chat --checkpoint checkpoints/best_model.pt`

## Future Enhancements

1. **模型改进**
   - 支持更大的模型配置
   - 实现更高效的attention机制
   - 添加位置编码的变体（如RoPE）

2. **训练改进**
   - 实现学习率调度器
   - 添加early stopping
   - 支持分布式训练

3. **推理改进**
   - 实现beam search
   - 添加nucleus sampling (top-p)
   - 支持流式生成

4. **用户体验**
   - 添加Web界面
   - 提供预训练模型下载
   - 可视化训练过程
