"""
Simple Tokenizer for LLM Training Project
Implements basic tokenization with vocabulary building and serialization.
"""

import json
from typing import List, Dict, Optional
from collections import Counter


class SimpleTokenizer:
    """
    基于词频统计的简单分词器
    
    特殊Token：
    - <PAD>: 0 (填充)
    - <UNK>: 1 (未知词)
    - <BOS>: 2 (序列开始)
    - <EOS>: 3 (序列结束)
    """
    
    # 特殊token定义
    PAD_TOKEN = '<PAD>'
    UNK_TOKEN = '<UNK>'
    BOS_TOKEN = '<BOS>'
    EOS_TOKEN = '<EOS>'
    
    def __init__(self, vocab_size: int = 2000):
        """
        初始化分词器
        
        Args:
            vocab_size: 词汇表大小（包含特殊token）
        """
        self.vocab_size = vocab_size
        self.token_to_id: Dict[str, int] = {}
        self.id_to_token: Dict[int, str] = {}
        
        # 初始化特殊token
        self.special_tokens = [
            self.PAD_TOKEN,
            self.UNK_TOKEN,
            self.BOS_TOKEN,
            self.EOS_TOKEN
        ]
        
        # 为特殊token分配ID
        for idx, token in enumerate(self.special_tokens):
            self.token_to_id[token] = idx
            self.id_to_token[idx] = token
    
    def build_vocab(self, texts: List[str]) -> None:
        """
        从文本列表构建词汇表（基于词频统计）
        
        Args:
            texts: 文本列表，用于统计词频
        """
        # 统计词频
        word_counter = Counter()
        for text in texts:
            # 简单的空格分词
            tokens = text.strip().split()
            word_counter.update(tokens)
        
        # 计算可用于普通词汇的空间（减去特殊token）
        num_special_tokens = len(self.special_tokens)
        available_vocab_size = self.vocab_size - num_special_tokens
        
        # 选择最常见的词汇
        most_common_words = word_counter.most_common(available_vocab_size)
        
        # 构建词汇表（特殊token已经在__init__中添加）
        current_id = num_special_tokens
        for word, _ in most_common_words:
            if word not in self.token_to_id:
                self.token_to_id[word] = current_id
                self.id_to_token[current_id] = word
                current_id += 1
    
    def encode(self, text: str, add_special_tokens: bool = True) -> List[int]:
        """
        将文本转换为token ID序列
        
        Args:
            text: 输入文本
            add_special_tokens: 是否添加BOS和EOS特殊token
            
        Returns:
            token ID列表
        """
        # 简单的空格分词
        tokens = text.strip().split()
        
        # 转换为ID，未知词使用UNK_TOKEN
        unk_id = self.token_to_id[self.UNK_TOKEN]
        token_ids = [self.token_to_id.get(token, unk_id) for token in tokens]
        
        # 添加特殊token
        if add_special_tokens:
            bos_id = self.token_to_id[self.BOS_TOKEN]
            eos_id = self.token_to_id[self.EOS_TOKEN]
            token_ids = [bos_id] + token_ids + [eos_id]
        
        return token_ids
    
    def decode(self, token_ids: List[int], skip_special_tokens: bool = True) -> str:
        """
        将token ID序列转换回文本
        
        Args:
            token_ids: token ID列表
            skip_special_tokens: 是否跳过特殊token
            
        Returns:
            解码后的文本字符串
        """
        tokens = []
        special_token_ids = {self.token_to_id[token] for token in self.special_tokens}
        
        for token_id in token_ids:
            # 跳过特殊token（如果需要）
            if skip_special_tokens and token_id in special_token_ids:
                continue
            
            # 获取对应的token
            token = self.id_to_token.get(token_id, self.UNK_TOKEN)
            tokens.append(token)
        
        # 用空格连接token
        return ' '.join(tokens)
    
    def save(self, path: str) -> None:
        """
        保存词汇表到JSON文件
        
        Args:
            path: 保存路径
        """
        vocab_data = {
            'vocab_size': self.vocab_size,
            'token_to_id': self.token_to_id,
            'special_tokens': self.special_tokens
        }
        
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(vocab_data, f, ensure_ascii=False, indent=2)
    
    def load(self, path: str) -> None:
        """
        从JSON文件加载词汇表
        
        Args:
            path: 加载路径
        """
        with open(path, 'r', encoding='utf-8') as f:
            vocab_data = json.load(f)
        
        self.vocab_size = vocab_data['vocab_size']
        self.token_to_id = vocab_data['token_to_id']
        self.special_tokens = vocab_data['special_tokens']
        
        # 重建id_to_token映射
        self.id_to_token = {int(id_): token for token, id_ in self.token_to_id.items()}
    
    def get_vocab_size(self) -> int:
        """返回当前词汇表大小"""
        return len(self.token_to_id)
    
    def get_vocab(self) -> Dict[str, int]:
        """返回词汇表的副本"""
        return self.token_to_id.copy()
    
    @property
    def pad_token_id(self) -> int:
        """返回PAD token的ID"""
        return self.token_to_id[self.PAD_TOKEN]
    
    @property
    def unk_token_id(self) -> int:
        """返回UNK token的ID"""
        return self.token_to_id[self.UNK_TOKEN]
    
    @property
    def bos_token_id(self) -> int:
        """返回BOS token的ID"""
        return self.token_to_id[self.BOS_TOKEN]
    
    @property
    def eos_token_id(self) -> int:
        """返回EOS token的ID"""
        return self.token_to_id[self.EOS_TOKEN]
