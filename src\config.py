"""
配置文件：定义模型和训练的配置数据类
"""
from dataclasses import dataclass
from typing import List


@dataclass
class ModelConfig:
    """模型架构配置"""
    vocab_size: int = 2000
    embed_dim: int = 64
    num_heads: int = 4
    num_layers: int = 2
    ff_dim: int = 128
    max_seq_len: int = 64
    dropout: float = 0.1


@dataclass
class TrainingConfig:
    """训练配置"""
    learning_rate: float = 0.001
    batch_size: int = 16
    num_epochs: int = 20
    max_seq_length: int = 64
    gradient_clip: float = 1.0
    checkpoint_interval: int = 5
    log_interval: int = 10
    train_split: float = 0.9
    device: str = 'cpu'


@dataclass
class TrainingState:
    """训练状态"""
    epoch: int = 0
    global_step: int = 0
    best_val_loss: float = float('inf')
    train_losses: List[float] = None
    val_losses: List[float] = None
    
    def __post_init__(self):
        if self.train_losses is None:
            self.train_losses = []
        if self.val_losses is None:
            self.val_losses = []
