# Implementation Plan

- [x] 1. 设置项目结构和基础配置





  - 创建项目目录结构（src/, data/, checkpoints/, logs/）
  - 创建requirements.txt文件，包含PyTorch、numpy、tqdm等依赖
  - 创建config.py文件，定义ModelConfig和TrainingConfig数据类
  - 创建README.md文件，说明项目用途和基本使用方法
  - _Requirements: 1.3, 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 2. 实现分词器模块





  - 创建src/tokenizer.py文件
  - 实现SimpleTokenizer类，包含特殊token定义（PAD, UNK, BOS, EOS）
  - 实现build_vocab方法，从文本构建词汇表（基于词频统计）
  - 实现encode方法，将文本转换为token ID序列
  - 实现decode方法，将token ID序列转换回文本
  - 实现save和load方法，支持词汇表的JSON序列化
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [ ] 3. 实现模型架构




  - 创建src/model.py文件
  - 实现SimpleLLM类，继承nn.Module
  - 实现token embedding层和position embedding层
  - 使用nn.TransformerEncoderLayer实现Transformer块（2层）
  - 实现输出层（layer normalization + linear projection）
  - 实现forward方法，接受input_ids返回logits
  - 配置模型参数：vocab_size=2000, embed_dim=64, num_heads=4, num_layers=2
  - 验证总参数量在90k-110k范围内
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 4. 实现数据处理模块
  - 创建src/data.py文件
  - 实现TextDataset类，继承torch.utils.data.Dataset
  - 实现_prepare_examples方法，将文本转换为(input, target)训练对
  - 实现__getitem__和__len__方法
  - 实现DataProcessor类的load_text_file静态方法
  - 实现create_dataloaders方法，支持训练/验证集分割和批处理
  - 实现序列padding和truncation逻辑
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5_

- [ ] 5. 实现训练引擎
  - 创建src/train.py文件
  - 实现Trainer类，初始化模型、优化器（Adam）和损失函数（CrossEntropyLoss）
  - 实现train_epoch方法，包含前向传播、损失计算、反向传播和参数更新
  - 实现梯度裁剪逻辑（gradient_clip=1.0）
  - 实现validate方法，在验证集上计算loss和perplexity
  - 实现训练指标的日志记录（每10个batch打印loss）
  - 实现train方法，完整的多epoch训练循环
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.6, 8.1, 8.2, 8.3_

- [ ] 6. 实现检查点保存和加载
  - 在Trainer类中实现save_checkpoint方法
  - 保存模型state_dict、优化器state_dict、配置和训练状态
  - 保存tokenizer词汇表到检查点
  - 实现load_checkpoint方法，从文件加载所有状态
  - 实现模型完整性验证（检查参数形状和配置一致性）
  - 在训练循环中添加定期保存逻辑（每5个epoch）
  - 保存验证loss最低的最佳模型
  - _Requirements: 4.5, 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 7. 实现推理引擎
  - 创建src/inference.py文件
  - 实现TextGenerator类，初始化时加载模型和tokenizer
  - 实现generate方法，支持文本生成
  - 实现greedy decoding策略（temperature=0）
  - 实现sampling策略，支持temperature和top_k参数
  - 实现生成长度控制（max_length参数）
  - 添加生成停止条件（遇到EOS token或达到最大长度）
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] 8. 实现交互式对话接口
  - 在TextGenerator类中实现chat方法
  - 创建命令行交互循环，接受用户输入
  - 调用generate方法生成回复并显示
  - 实现退出命令（输入"exit"或"quit"）
  - 添加友好的提示信息和错误处理
  - _Requirements: 6.5_

- [ ] 9. 创建主程序入口
  - 创建main.py文件
  - 使用argparse实现命令行参数解析
  - 添加--mode参数（train/chat）
  - 添加--data参数（训练数据路径）
  - 添加--checkpoint参数（模型检查点路径）
  - 添加其他可选参数（learning_rate, batch_size, epochs等）
  - 实现train_model函数，整合tokenizer、数据加载、模型初始化和训练流程
  - 实现chat_with_model函数，加载检查点并启动对话
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

- [ ] 10. 准备示例训练数据
  - 创建data/train.txt文件
  - 添加简单的中文对话或文本数据（至少100行）
  - 确保数据格式适合训练（每行一个句子或对话）
  - _Requirements: 3.1_

- [ ] 11. 实现训练日志和监控
  - 在Trainer类中添加日志文件写入功能
  - 记录每个epoch的训练loss、验证loss和perplexity
  - 使用tqdm显示训练进度条
  - 计算并显示预估剩余时间
  - 将日志保存到logs/目录
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [ ] 12. 端到端测试和调试
  - 运行完整的训练流程，确保模型能够正常训练
  - 验证训练loss是否下降
  - 检查检查点文件是否正确保存
  - 加载训练好的模型进行推理测试
  - 测试对话功能，验证生成文本的基本合理性
  - 调整超参数以优化训练效果
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 6.1, 6.2, 6.3, 6.4, 6.5_
