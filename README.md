# LLM Training Project

一个从零开始构建的轻量级大语言模型（LLM）训练系统。本项目实现了一个参数量约为10万的小型Transformer语言模型，提供完整的训练和推理功能。

## 项目简介

这是一个教育性质的项目，旨在帮助理解大语言模型的核心原理和实现细节。项目包含：

- 基于Transformer的模型架构（约10万参数）
- 简单的分词器实现
- 完整的训练流程
- 交互式对话推理接口

## 项目结构

```
llm-training-project/
├── src/                  # 源代码目录
│   ├── config.py        # 配置定义
│   ├── model.py         # 模型架构
│   ├── tokenizer.py     # 分词器
│   ├── data.py          # 数据处理
│   ├── train.py         # 训练引擎
│   └── inference.py     # 推理引擎
├── data/                # 训练数据目录
│   └── train.txt        # 训练文本数据
├── checkpoints/         # 模型检查点保存目录
├── logs/                # 训练日志目录
├── main.py              # 主程序入口
├── requirements.txt     # Python依赖
└── README.md           # 项目说明
```

## 环境要求

- Python >= 3.8
- PyTorch >= 1.10
- numpy >= 1.21
- tqdm >= 4.62

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 准备训练数据

将训练文本数据放入 `data/train.txt` 文件中。每行一个句子或对话，确保有足够的数据量（建议至少100行）。

### 2. 训练模型

```bash
python main.py --mode train --data data/train.txt
```

可选参数：
- `--learning_rate`: 学习率（默认：0.001）
- `--batch_size`: 批次大小（默认：16）
- `--epochs`: 训练轮数（默认：20）
- `--checkpoint`: 从检查点继续训练

### 3. 对话测试

训练完成后，使用训练好的模型进行交互式对话：

```bash
python main.py --mode chat --checkpoint checkpoints/best_model.pt
```

在对话界面中输入文本，模型会生成回复。输入 `exit` 或 `quit` 退出对话。

## 模型配置

默认模型配置（约10万参数）：
- 词汇表大小：2000
- 嵌入维度：64
- 注意力头数：4
- Transformer层数：2
- 最大序列长度：64

可以在 `src/config.py` 中修改这些配置。

## 训练监控

训练过程中会显示：
- 当前epoch和batch
- 训练loss
- 验证loss和perplexity
- 预估剩余时间

训练日志会保存到 `logs/` 目录，模型检查点会定期保存到 `checkpoints/` 目录。

## 注意事项

1. 这是一个教育项目，模型规模较小，生成质量有限
2. 建议在有GPU的环境下训练以提高速度
3. 训练数据的质量直接影响模型效果
4. 首次训练建议使用较小的数据集进行测试

## 许可证

本项目仅供学习和研究使用。
