"""
模型架构：实现基于Transformer的简化语言模型
"""
import torch
import torch.nn as nn
import math
from src.config import ModelConfig


class SimpleLLM(nn.Module):
    """
    简化的Transformer语言模型
    
    参数配置（目标：~100k参数）：
    - vocab_size: 2000 (词汇表大小)
    - embed_dim: 64 (嵌入维度)
    - num_heads: 4 (注意力头数)
    - num_layers: 2 (Transformer层数)
    - ff_dim: 128 (前馈网络维度)
    - max_seq_len: 64 (最大序列长度)
    """
    
    def __init__(self, config: ModelConfig):
        super(SimpleLLM, self).__init__()
        self.config = config
        
        # Token embedding层
        self.token_embedding = nn.Embedding(config.vocab_size, config.embed_dim)
        
        # Position embedding层
        self.position_embedding = nn.Embedding(config.max_seq_len, config.embed_dim)
        
        # Dropout层
        self.dropout = nn.Dropout(config.dropout)
        
        # Transformer编码器层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=config.embed_dim,
            nhead=config.num_heads,
            dim_feedforward=config.ff_dim,
            dropout=config.dropout,
            activation='relu',
            batch_first=True
        )
        
        self.transformer_blocks = nn.TransformerEncoder(
            encoder_layer,
            num_layers=config.num_layers
        )
        
        # 输出层
        self.ln_f = nn.LayerNorm(config.embed_dim)
        self.lm_head = nn.Linear(config.embed_dim, config.vocab_size, bias=False)
        
        # 权重共享：token embedding和输出层共享权重
        self.lm_head.weight = self.token_embedding.weight
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化模型权重"""
        # 初始化embedding层
        nn.init.normal_(self.token_embedding.weight, mean=0.0, std=0.02)
        nn.init.normal_(self.position_embedding.weight, mean=0.0, std=0.02)
        
        # 初始化LayerNorm
        nn.init.ones_(self.ln_f.weight)
        nn.init.zeros_(self.ln_f.bias)
    
    def forward(self, input_ids, attention_mask=None):
        """
        前向传播
        
        参数：
            input_ids: torch.Tensor, shape (batch_size, seq_len)
            attention_mask: torch.Tensor, shape (batch_size, seq_len), 可选
        
        返回：
            logits: torch.Tensor, shape (batch_size, seq_len, vocab_size)
        """
        batch_size, seq_len = input_ids.shape
        
        # 生成位置索引
        position_ids = torch.arange(seq_len, dtype=torch.long, device=input_ids.device)
        position_ids = position_ids.unsqueeze(0).expand(batch_size, -1)
        
        # Token embedding + Position embedding
        token_embeds = self.token_embedding(input_ids)
        position_embeds = self.position_embedding(position_ids)
        hidden_states = token_embeds + position_embeds
        hidden_states = self.dropout(hidden_states)
        
        # 创建因果注意力掩码（causal mask）
        # Transformer需要上三角掩码来防止看到未来的token
        causal_mask = self._generate_square_subsequent_mask(seq_len).to(input_ids.device)
        
        # 如果提供了padding mask，需要转换格式
        # PyTorch的TransformerEncoder期望mask为True的位置被忽略
        src_key_padding_mask = None
        if attention_mask is not None:
            # attention_mask: 1表示有效token，0表示padding
            # src_key_padding_mask: True表示padding，False表示有效token
            src_key_padding_mask = (attention_mask == 0)
        
        # 通过Transformer层
        hidden_states = self.transformer_blocks(
            hidden_states,
            mask=causal_mask,
            src_key_padding_mask=src_key_padding_mask
        )
        
        # 输出层
        hidden_states = self.ln_f(hidden_states)
        logits = self.lm_head(hidden_states)
        
        return logits
    
    def _generate_square_subsequent_mask(self, sz):
        """
        生成因果注意力掩码（上三角矩阵）
        
        参数：
            sz: int, 序列长度
        
        返回：
            mask: torch.Tensor, shape (sz, sz)
        """
        mask = torch.triu(torch.ones(sz, sz), diagonal=1).bool()
        return mask
    
    def count_parameters(self):
        """计算模型总参数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)
    
    def get_parameter_breakdown(self):
        """获取各层参数量的详细分解"""
        breakdown = {}
        breakdown['token_embedding'] = self.token_embedding.weight.numel()
        breakdown['position_embedding'] = self.position_embedding.weight.numel()
        
        # Transformer层参数
        transformer_params = sum(p.numel() for p in self.transformer_blocks.parameters() if p.requires_grad)
        breakdown['transformer_blocks'] = transformer_params
        
        # LayerNorm参数
        breakdown['ln_f'] = sum(p.numel() for p in self.ln_f.parameters() if p.requires_grad)
        
        # 注意：lm_head与token_embedding共享权重，不重复计算
        breakdown['total'] = self.count_parameters()
        
        return breakdown


def create_model(config: ModelConfig = None):
    """
    创建模型实例的工厂函数
    
    参数：
        config: ModelConfig, 模型配置，如果为None则使用默认配置
    
    返回：
        model: SimpleLLM实例
    """
    if config is None:
        config = ModelConfig()
    
    model = SimpleLLM(config)
    return model


if __name__ == "__main__":
    # 测试模型创建和参数量
    print("创建模型...")
    config = ModelConfig()
    model = create_model(config)
    
    print(f"\n模型配置:")
    print(f"  词汇表大小: {config.vocab_size}")
    print(f"  嵌入维度: {config.embed_dim}")
    print(f"  注意力头数: {config.num_heads}")
    print(f"  Transformer层数: {config.num_layers}")
    print(f"  前馈网络维度: {config.ff_dim}")
    print(f"  最大序列长度: {config.max_seq_len}")
    
    print(f"\n参数量统计:")
    breakdown = model.get_parameter_breakdown()
    for name, count in breakdown.items():
        print(f"  {name}: {count:,}")
    
    total_params = model.count_parameters()
    print(f"\n总参数量: {total_params:,}")
    
    # 验证参数量是否在目标范围内
    if 90000 <= total_params <= 110000:
        print("✓ 参数量在目标范围内 (90k-110k)")
    else:
        print(f"✗ 参数量不在目标范围内 (当前: {total_params}, 目标: 90k-110k)")
    
    # 测试前向传播
    print("\n测试前向传播...")
    batch_size = 2
    seq_len = 10
    input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_len))
    
    with torch.no_grad():
        logits = model(input_ids)
    
    print(f"  输入形状: {input_ids.shape}")
    print(f"  输出形状: {logits.shape}")
    print(f"  预期输出形状: ({batch_size}, {seq_len}, {config.vocab_size})")
    
    if logits.shape == (batch_size, seq_len, config.vocab_size):
        print("✓ 输出形状正确")
    else:
        print("✗ 输出形状不正确")
    
    print("\n模型创建成功！")
